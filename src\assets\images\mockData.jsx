import img1 from "./ابو-خنجر-امامي-400x457.jpg";
import img2 from "./اقحوان-ام-400x457.jpg";
import img3 from "./حار-اخضر-امامي-400x457.jpg";
import img4 from "./خبيزة-امامي-400x457.jpg";
import img5 from "./شمر-امامي-400x457.jpg";
import img6 from "./فراولة-امامي-400x457.jpg";
import img7 from "./كمثرى-صفرا-امامى-400x457.jpg";
import img8 from "./وينكا-امامي-400x457.jpg";
import img9 from "./ينسون-امامي-400x457.jpg";
import image12 from "./2 (2).png";
import image13 from "./4 (1).png";
import image14 from "./3.png";
import image15 from "./5.png";
import image16 from "./6.png";
import image17 from "./7.png";
import image18 from "./8.png";
import image19 from "./image-2.jpg";
import image20 from "./image-3.jpg";
import image21 from "./image-4.jpg";
import image22 from "./image-5.jpg";
import image23 from "./image-6 (1).jpg";
import image26 from "./image-7.jpg";
import image24 from "./image-11.jpg";
import image25 from "./image-12.jpg";

import image30 from "./OrganicPesticides/1613051031تاجليس.jpg";
import image32 from "./OrganicPesticides/1613936763برفكتووان.jpg";
import image31 from "./OrganicPesticides/1613936859توبناين.jpg";
import image33 from "./OrganicPesticides/1624802097tehnooil.jpg";
import image34 from "./OrganicPesticides/1639311621image00025.png";
import image35 from "./OrganicPesticides/1641207507image00002.png";
import image36 from "./OrganicPesticides/1691318586zentari.jpg";
import image37 from "./OrganicPesticides/1692103940كوفريت.jpg";

import image70 from "./Herbicides/1613124523اتلانت.jpg";
import image71 from "./Herbicides/1613129904تايجرفوس.jpg";
import image72 from "./Herbicides/1613936534باسفوليارسوبر.jpg";
import image73 from "./Herbicides/1613936784بلنتبرود.jpg";
import image74 from "./Herbicides/1613937037بوباكسلارج.jpeg";
import image75 from "./Herbicides/1613937641جرين.jpg";
import image76 from "./Herbicides/1613937811دلتاميكرو.jpg";
import image77 from "./Herbicides/1613937962روتباور.jpg";
import image78 from "./Herbicides/17272567911000088067.png";

import image100 from "./insecticides/1613129111افينيو.jpg";
import image101 from "./insecticides/1613130137اييزو.jpg";
import image102 from "./insecticides/1613130904بيني.jpg";
import image103 from "./insecticides/1613135142شينوك.jpg";
import image104 from "./insecticides/1613931825برناستار.jpg";
import image105 from "./insecticides/1623152749chinook.jpg";
import image106 from "./insecticides/1623154800penny.jpg";
import image107 from "./insecticides/1699792003كاجورا.png";

import image108 from "./productsimgs/Beans Green - فاصوليا خضراء.jpeg";
import image109 from "./productsimgs/Blueberry - توت بري.jpeg";
import image110 from "./productsimgs/Capsicum Yellow - فليفلة صفرا.jpg";
import image111 from "./productsimgs/Coconut Fresh Whole - جوز هند حبة كامل.webp";
import image112 from "./productsimgs/Colored Capsicum - فيلفلة ملونة.webp";
import image113 from "./productsimgs/Coriander -كزبرة.webp";
import image114 from "./productsimgs/Grape Leaves Green Fresh ورق عنب اخضر.webp";
import image115 from "./productsimgs/Guava - جوافة.webp";
import image116 from "./productsimgs/Iceberg Lettuce - خس مدور.webp";
import image117 from "./productsimgs/Kiwi Iranian - كيوي.webp";
import image118 from "./productsimgs/Indian Onion - بصل هندي.webp";
import image119 from "./productsimgs/Mango مانجو.webp";
import image120 from "./productsimgs/Lemon Green - ليمون أخضر.jpeg";
import image121 from "./productsimgs/Pineapple - أناناس.webp";
import image122 from "./productsimgs/Plums Yellow - خوخ أصفر.jpg";
import image123 from "./productsimgs/Pomegranate - رمان.jpg";
import image124 from "./productsimgs/Potato egypt - بطاطا مصرية.jpeg";
export const mockData = [
  {
    id: 1,
    image: img1,
    name: "Nasturtium",
    price: 39.99,
    description:
      "A vibrant edible flower with peppery flavor, often used in salads and garnishes.",
    category: "Crop",
  },
  {
    id: 2,
    image: img2,
    name: "Chrysant",
    price: 29.99,
    description:
      "Chrysanthemum, known for its beautiful blooms, is used in teas and traditional medicine.",
    category: "Crop",
  
  },
  {
    id: 3,
    image: img3,
    name: "Green Chili ",
    price: 49.99,
    description:
      "Fresh green chili peppers, perfect for adding heat and flavor to dishes.",
    category: "Crop",
    startDate: "2023-02-20",
    duration: 80,
    endDate: "2023-05-11",
  },
  {
    id: 4,
    image: img4,
    name: "Mallow ",
    price: 19.99,
    description:
      "Mallow leaves are used in soups and stews, known for their mild flavor and health benefits.",
    category: "Crop",
    startDate: "2023-03-10",
    duration: 85,
    endDate: "2023-06-03",
  },
  {
    id: 5,
    image: img5,
    name: "Fennel",
    price: 39.1,
    description:
      "Fennel is a crunchy, slightly sweet vegetable with a flavor similar to licorice.",
    category: "Crop",
    startDate: "2023-04-01",
    duration: 70,
    endDate: "2023-06-10",
  },
  {
    id: 6,
    image: img6,
    name: "Strawberry",
    price: 50.99,
    description:
      "Juicy and sweet strawberries, perfect for desserts and snacks.",
    category: "Crop",
    startDate: "2023-03-25",
    duration: 95,
    endDate: "2023-06-28",
  },
  {
    id: 7,
    image: img7,
    name: "Yellow Pear",
    price: 29.99,
    description:
      "Yellow pears are sweet and juicy, great for eating fresh or in salads.",
    category: "Crop",
    startDate: "2023-04-05",
    duration: 60,
    endDate: "2023-06-04",
  },
  {
    id: 8,
    image: img8,
    name: " Periwinkle",
    price: 49.99,
    description:
      "Periwinkle flowers are known for their vibrant colors and are often used in gardens.",
    category: "Crop",
    startDate: "2023-03-15",
    duration: 75,
    endDate: "2023-05-29",
  },
  {
    id: 9,
    image: img9,
    name: "jhins",
    price: 79.99,
    description:
      "Anise seeds have a sweet, licorice-like flavor and are used in cooking and baking.",
    category: "Crop",
    startDate: "2023-02-28",
    duration: 90,
    endDate: "2023-05-29",
  },
  {
    id: 10,
    image: img1,
    name: "Nasturtium",
    price: 39.99,
    description:
      "A vibrant edible flower with peppery flavor, often used in salads and garnishes.",
    category: "Crop",
  },
  {
    id: 11,
    image: img2,
    name: "Chrysant",
    price: 29.99,
    description:
      "Chrysanthemum, known for its beautiful blooms, is used in teas and traditional medicine.",
    category: "Crop",
    startDate: "2023-04-15",
    duration: 75,
    endDate: "2023-06-29",
  },
  {
    id: 12,
    image: img3,
    name: "Green Chili ",
    price: 49.99,
    description:
      "Fresh green chili peppers, perfect for adding heat and flavor to dishes.",
    category: "Crop",
    startDate: "2023-02-20",
    duration: 80,
    endDate: "2023-05-11",
  },
  {
    id: 13,
    image: img5,
    name: "Fennel",
    price: 39.1,
    description:
      "Fennel is a crunchy, slightly sweet vegetable with a flavor similar to licorice.",
    category: "Crop",
    startDate: "2023-04-01",
    duration: 70,
    endDate: "2023-06-10",
  },
  {
    id: 14,
    image: img6,
    name: "Strawberry",
    price: 50.99,
    description:
      "Juicy and sweet strawberries, perfect for desserts and snacks.",
    category: "Crop",
    startDate: "2023-03-25",
    duration: 95,
    endDate: "2023-06-28",
  },
  {
    id: 15,
    image: img7,
    name: "Yellow Pear",
    price: 29.99,
    description:
      "Yellow pears are sweet and juicy, great for eating fresh or in salads.",
    category: "Crop",
    startDate: "2023-04-05",
    duration: 60,
    endDate: "2023-06-04",
  },
  {
    id: 16,
    image: img8,
    name: " Periwinkle",
    price: 49.99,
    description:
      "Periwinkle flowers are known for their vibrant colors and are often used in gardens.",
    category: "Crop",
    startDate: "2024-03-15",
    duration: 75,
    endDate: "2025-11-5",
  },
  {
    id: 17,
    image: img9,
    name: " Anise",
    price: 79.99,
    description:
      "Anise seeds have a sweet, licorice-like flavor and are used in cooking and baking.",
    category: "Crop",
    startDate: "2025-02-28",
    duration: 90,
    endDate: "2025-12-29",
  },
];
export const mockData2 = [
  {
    id: 66,
    image: image108,
    name: "Beans Green",
    price: 90,
    description: "Fresh green beans, perfect for cooking and salads.",
    category: "Vegetable",
  },
  {
    id: 67,
    image: image109,
    name: "Blueberry",
    price: 120,
    description: "Sweet and juicy blueberries, rich in antioxidants.",
    category: "Fruit",
  },
  {
    id: 68,
    image: image110,
    name: "Capsicum Yellow",
    price: 60,
    description: "Bright yellow capsicum, great for salads and cooking.",
    category: "Vegetable",
  },
  {
    id: 69,
    image: image111,
    name: "Coconut Fresh Whole",
    price: 150,
    description: "Fresh whole coconut, perfect for cooking and drinking.",
    category: "Fruit",
  },
  {
    id: 70,
    image: image112,
    name: "Colored Capsicum",
    price: 70,
    description: "Mixed colored capsicum, adds color and flavor to dishes.",
    category: "Vegetable",
  },
  {
    id: 71,
    image: image113,
    name: "Coriander",
    price: 40,
    description: "Fresh coriander leaves, used as a herb and garnish.",
    category: "Herb",
  },
  {
    id: 72,
    image: image114,
    name: "Grape Leaves Green Fresh",
    price: 55,
    description: "Fresh green grape leaves, used in cooking and wraps.",
    category: "Vegetable",
  },
  {
    id: 73,
    image: image115,
    name: "Guava",
    price: 80,
    description: "Sweet and tropical guava fruit, rich in vitamin C.",
    category: "Fruit",
  },
  {
    id: 74,
    image: image116,
    name: "Iceberg Lettuce",
    price: 35,
    description: "Crisp iceberg lettuce, perfect for salads and sandwiches.",
    category: "Vegetable",
  },
  {
    id: 75,
    image: image117,
    name: "Kiwi Iranian",
    price: 90,
    description: "Tangy and sweet Iranian kiwi fruit.",
    category: "Fruit",
  },
  {
    id: 76,
    image: image118,
    name: "Indian Onion",
    price: 25,
    description: "Fresh Indian onions, used in various cuisines.",
    category: "Vegetable",
  },
  {
    id: 77,
    image: image119,
    name: "Mango",
    price: 110,
    description: "Sweet and juicy mango, a tropical favorite.",
    category: "Fruit",
  },
  {
    id: 78,
    image: image120,
    name: "Lemon Green",
    price: 30,
    description: "Fresh green lemons, perfect for cooking and drinks.",
    category: "Fruit",
  },
  {
    id: 79,
    image: image121,
    name: "Pineapple",
    price: 95,
    description: "Tropical pineapple, sweet and tangy flavor.",
    category: "Fruit",
  },
  {
    id: 80,
    image: image122,
    name: "Plums Yellow",
    price: 65,
    description: "Sweet yellow plums, great for snacking and desserts.",
    category: "Fruit",
  },
  {
    id: 81,
    image: image123,
    name: "Pomegranate",
    price: 85,
    description: "Juicy pomegranate seeds, rich in antioxidants.",
    category: "Fruit",
  },
  {
    id: 82,
    image: image124,
    name: "Potato Egypt",
    price: 20,
    description: "Fresh Egyptian potatoes, versatile for cooking.",
    category: "Vegetable",
  },

  {
    id: 84,
    image: image12,
    name: "Carrot",
    price: 29.99,
    description: "Crunchy and sweet carrots, great for salads and snacking.",
    category: "Vegetable",
  },
  {
    id: 85,
    image: image13,
    name: "Cabbage",
    price: 49.99,
    description: "Fresh cabbage, perfect for salads and cooking.",
    category: "Vegetable",
  },
  {
    id: 86,
    image: image14,
    name: "Grapes",
    price: 19.99,
    description: "Fresh Grapes, rich in vitamins and minerals.",
    category: "Fruit",
  },
  {
    id: 87,
    image: image15,
    name: "Cauliflower",
    price: 39.1,
    description: "Fresh cauliflower, versatile for cooking and salads.",
    category: "Vegetable",
  },
  {
    id: 88,
    image: image16,
    name: "Banana",
    price: 50.99,
    description: "Fresh bananas, perfect for snacking and baking.",
    category: "Fruit",
  },
  {
    id: 89,
    image: image17,
    name: "Bell Pepper",
    price: 29.99,
    description: "Colorful bell peppers, perfect for salads and stir-fries.",
    category: "Vegetable",
  },
  {
    id: 90,
    image: image18,
    name: "Avocado",
    price: 49.99,
    description: " Fresh  avocados, perfect for salads and sandwiches.",
    category: "Fruit",
  },
  {
    id: 91,
    image: image19,
    name: "Kewi",
    price: 79.99,
    description: "Tangy and sweet Iranian kiwi fruit.",
    category: "Fruit",
  },
  {
    id: 92,
    image: image20,
    name: "Orange",
    price: 39.99,
    description: "Fresh oranges, rich in vitamin C.",
    category: "Fruit",
  },
  {
    id: 93,
    image: image21,
    name: "Radish",
    price: 29.99,
    description: "Fresh radishes, perfect for salads and garnishes.",
    category: "Vegetable",
  },
];

export const mockData3 = [
  {
    id: 30,
    image: image30,
    name: "Tagless Pesticide",
    price: 39.99,
    description:
      "A natural pesticide used to protect plants from pests and diseases.",
    category: "Organic Pesticide",
  },
  {
    id: 40,
    image: image31,
    name: "TopNine Pesticide",
    price: 29.99,
    description:
      "An organic pesticide effective against a wide range of insects.",
    category: "Organic Pesticide",
  },
  {
    id: 32,
    image: image32,
    name: "PerfectOne Pesticide",
    price: 49.99,
    description: "A highly effective organic pesticide for controlling pests.",
    category: "Organic Pesticide",
  },
  {
    id: 33,
    image: image33,
    name: "TechnoOil Pesticide",
    price: 19.99,
    description: "An organic oil used as a pesticide to control various pests.",
    category: "Organic Pesticide",
  },
  {
    id: 34,
    image: image34,
    name: "NaturalGuard Pesticide",
    price: 39.1,
    description:
      "An organic pesticide used to protect plants from pests and diseases.",
    category: "Organic Pesticide",
  },
  {
    id: 35,
    image: image35,
    name: "EcoShield Pesticide",
    price: 50.99,
    description:
      "A natural pesticide used to protect plants from pests and diseases.",
    category: "Organic Pesticide",
  },
  {
    id: 36,
    image: image36,
    name: "Zentari Pesticide",
    price: 29.99,
    description:
      "An organic pesticide effective against a wide range of insects.",
    category: "Organic Pesticide",
  },
  {
    id: 37,
    image: image37,
    name: "Covrite Pesticide",
    price: 49.99,
    description: "A highly effective organic pesticide for controlling pests.",
    category: "Organic Pesticide",
  },
];

export const mockData4 = [
  {
    id: 70,
    image: image70,
    name: "Atlant Herbicide",
    price: 39.99,
    description: "A herbicide used to control a wide range of weeds.",
    category: "Herbicide",
  },
  {
    id: 71,
    image: image71,
    name: "TigerPhos Herbicide",
    price: 29.99,
    description:
      "An effective herbicide for controlling various types of weeds.",
    category: "Herbicide",
  },
  {
    id: 72,
    image: image72,
    name: "BasFoliar Super",
    price: 49.99,
    description: "A highly effective herbicide for controlling tough weeds.",
    category: "Herbicide",
  },
  {
    id: 73,
    image: image73,
    name: "PlantBroad Herbicide",
    price: 19.99,
    description: "A herbicide used to control a wide range of weeds.",
    category: "Herbicide",
  },
  {
    id: 74,
    image: image74,
    name: "BubacsLarge Herbicide",
    price: 39.1,
    description:
      "An effective herbicide for controlling various types of weeds.",
    category: "Herbicide",
  },
  {
    id: 75,
    image: image75,
    name: "Green Herbicide",
    price: 50.99,
    description: "A highly effective herbicide for controlling tough weeds.",
    category: "Herbicide",
  },
  {
    id: 76,
    image: image76,
    name: "DeltaMicro Herbicide",
    price: 29.99,
    description: "A herbicide used to control a wide range of weeds.",
    category: "Herbicide",
  },
  {
    id: 77,
    image: image77,
    name: "RootPower Herbicide",
    price: 49.99,
    description:
      "An effective herbicide for controlling various types of weeds.",
    category: "Herbicide",
  },
  {
    id: 78,
    image: image78,
    name: "WeedGuard Herbicide",
    price: 19.99,
    description: "A herbicide used to control a wide range of weeds.",
    category: "Herbicide",
  },
];

export const mockData5 = [
  {
    id: 100,
    image: image100,
    name: "Avenue Insecticide",
    price: 39.99,
    description:
      "A powerful insecticide used to control a wide range of pests.",
    category: "Insecticide",
  },
  {
    id: 101,
    image: image101,
    name: "EasyZo Insecticide",
    price: 29.99,
    description:
      "An effective insecticide for controlling various types of insects.",
    category: "Insecticide",
  },
  {
    id: 102,
    image: image102,
    name: "Penny Insecticide",
    price: 49.99,
    description: "A highly effective insecticide for controlling tough pests.",
    category: "Insecticide",
  },
  {
    id: 103,
    image: image103,
    name: "Chinook Insecticide",
    price: 19.99,
    description: "An insecticide used to control a wide range of pests.",
    category: "Insecticide",
  },
  {
    id: 104,
    image: image104,
    name: "BernaStar Insecticide",
    price: 39.1,
    description:
      "An effective insecticide for controlling various types of insects.",
    category: "Insecticide",
  },
  {
    id: 105,
    image: image105,
    name: "Chinook Premium",
    price: 50.99,
    description: "A highly effective insecticide for controlling tough pests.",
    category: "Insecticide",
  },
  {
    id: 106,
    image: image106,
    name: "Penny Plus",
    price: 29.99,
    description: "An insecticide used to control a wide range of pests.",
    category: "Insecticide",
  },
  {
    id: 107,
    image: image107,
    name: "Kagura Insecticide",
    price: 49.99,
    description:
      "An effective insecticide for controlling various types of insects.",
    category: "Insecticide",
  },
];

// Helper to ensure unique IDs and trimmed categories
defaultExportCleanup();

function defaultExportCleanup() {
  // Helper to generate unique IDs
  let nextId = 1;
  const usedIds = new Set();
  function getUniqueId() {
    while (usedIds.has(nextId)) nextId++;
    usedIds.add(nextId);
    return nextId++;
  }

  // Clean a data array: assign unique IDs and trim category
  function cleanArray(arr) {
    arr.forEach((item) => {
      item.id = getUniqueId();
      if (item.category) item.category = item.category.trim();
    });
  }

  [mockData, mockData2, mockData3, mockData4, mockData5].forEach(cleanArray);
}


body {
    font-family: '<PERSON>pin<PERSON>', sans-serif;
}

h1,
h2,
h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    /* Makes headlines bold */
}

p {
    font-family: 'Poppins', sans-serif;
    font-weight: 300;
}

.wave {
    background-color: #0b3d02;
    height: 50px;
    clip-path: polygon(0 50%, 10% 60%, 20% 50%, 30% 60%, 40% 50%, 50% 60%, 60% 50%, 70% 60%, 80% 50%, 90% 60%, 100% 50%, 100% 100%, 0% 100%);
}

/* Hero Section */
.hero-section {
    position: relative;
    color: white;
    text-align: center;

}

/* Small Tagline */
.tagline {
    font-size: 1.1rem;
    color: #FFC107;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Hero Title */
.hero-title {
    font-size: 3rem;
    font-weight: bold;
    font-family: 'Caveat', cursive;
    /* Handwritten style */
    color: white !important;
}

/* Yellow Underline */
.underline {
    width: 350px;
    height: 5px;
    background: #FFC107;
    margin: 10px auto;
}

/* Hero Text */
.hero-text {
    font-size: 1rem;
    max-width: 60%;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

/* Button */
.hero-button {
    background: #fff;
    color: black;
    font-size: 1.2rem;
    padding: 10px 20px;
    border: none;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
    transition: 0.3s;
}

.hero-button:hover {
    background: #FFC107;
}

/* Arrow */
.arrow {
    background: #FFC107;
    color: black;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* Grass Wave */
.grass-wave {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 50px;
    background: url('/src/assets/images/grass-wave.png') repeat-x;
    background-size: contain;
}




@keyframes moveBackground {
    0% {
        background-position: 100% center;
    }

    100% {
        background-position: 0% center;
    }
}

.hero-section {
    animation: moveBackground 14s linear infinite alternate;
    background-size: cover;
    background-position: 100% center;
    -webkit-animation: moveBackground 14s linear infinite alternate;
}
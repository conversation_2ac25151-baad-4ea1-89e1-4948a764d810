/* Services Page Styles */
.services-page {
  font-family: 'Poppins', sans-serif;
}

/* Hero Section */
.services-hero {
  position: relative;
  padding: 80px 0 50px;
  background-color: #f8f9fa;
  overflow: hidden;
}

.services-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
  animation: gradientShift 8s ease infinite alternate;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.hero-title {
  position: relative;
  animation: slideInLeft 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: #0b3d02;
  font-weight: 700;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 0;
  height: 3px;
  background-color: #FFC107;
  animation: lineExpand 1.5s ease-out forwards;
  animation-delay: 0.8s;
}

@keyframes lineExpand {
  0% {
    width: 0;
  }
  100% {
    width: 80px;
  }
}

.hero-subtitle {
  animation: slideInRight 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation-delay: 0.3s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.services-hero .btn {
  animation: popUp 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation-delay: 0.6s;
  opacity: 0;
  animation-fill-mode: forwards;
  transform-origin: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
}

.services-hero .btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 10px 25px rgba(76, 175, 80, 0.3);
  background-color: #3a9d3a;
}

.hero-image {
  position: relative;
  animation: floatIn 1.5s ease-out;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-radius: 15px;
  overflow: hidden;
}

.hero-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
  animation: shimmer 3s infinite;
  animation-delay: 1.5s;
}

/* New keyframe animations */
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes popUp {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  70% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes floatIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 150%;
  }
}

/* Floating animation for hero image */
.hero-image {
  animation: float 6s ease-in-out infinite;
  animation-delay: 1.5s;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Zigzag/wave separator with animation */
.services-hero::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 25px;
  background-color: #ffffff;
  clip-path: polygon(
    0% 0%, 5% 100%, 10% 0%, 15% 100%, 
    20% 0%, 25% 100%, 30% 0%, 35% 100%, 
    40% 0%, 45% 100%, 50% 0%, 55% 100%, 
    60% 0%, 65% 100%, 70% 0%, 75% 100%, 
    80% 0%, 85% 100%, 90% 0%, 95% 100%, 
    100% 0%, 100% 100%, 0% 100%
  );
  animation: zigzagMove 3s ease-in-out infinite alternate;
}

@keyframes zigzagMove {
  0% {
    clip-path: polygon(
      0% 0%, 5% 100%, 10% 0%, 15% 100%, 
      20% 0%, 25% 100%, 30% 0%, 35% 100%, 
      40% 0%, 45% 100%, 50% 0%, 55% 100%, 
      60% 0%, 65% 100%, 70% 0%, 75% 100%, 
      80% 0%, 85% 100%, 90% 0%, 95% 100%, 
      100% 0%, 100% 100%, 0% 100%
    );
  }
  100% {
    clip-path: polygon(
      0% 100%, 5% 0%, 10% 100%, 15% 0%, 
      20% 100%, 25% 0%, 30% 100%, 35% 0%, 
      40% 100%, 45% 0%, 50% 100%, 55% 0%, 
      60% 100%, 65% 0%, 70% 100%, 75% 0%, 
      80% 100%, 85% 0%, 90% 100%, 95% 0%, 
      100% 100%, 100% 100%, 0% 100%
    );
  }
}

/* Ensure the next section has proper spacing */
.services-overview {
  position: relative;
  z-index: 3;
  background-color: #fff;
  padding-top: 40px;
}

/* Services Overview Section */
.services-overview {
  padding: 80px 0;
  background-color: #fff;
}

.services-overview .row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.services-overview .col-lg-3 {
  margin-bottom: 30px;
}

/* Make service cards equal height */
.service-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Push button to bottom of card */
.service-card .btn {
  margin-top: auto;
}

/* Responsive adjustments for 5 cards */
@media (min-width: 992px) and (max-width: 1199px) {
  .services-overview .col-lg-3 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
}

@media (max-width: 991px) {
  .services-overview .col-lg-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 575px) {
  .services-overview .col-lg-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.section-title {
  font-size: 2.2rem;
  color: #0b3d02;
  margin-bottom: 50px;
  position: relative;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: #FFC107;
}

.service-card {
  background-color: #fff;
  border-radius: 10px;
  padding: 30px 20px;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
              box-shadow 0.4s ease, 
              background-color 0.3s ease;
  overflow: hidden;
  position: relative;
  text-align: center;
  margin-bottom: 30px;
}

.service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  background-color: #f9fff9;
}

.icon-container {
  width: 70px;
  height: 70px;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.service-card:hover .icon-container {
  transform: scale(1.1) rotate(5deg);
  background-color: rgba(76, 175, 80, 0.2);
}

.service-icon {
  color: #4caf50;
  font-size: 30px;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.2);
}

.service-card h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 15px;
}

.service-card p {
  color: #666;
  margin-bottom: 20px;
}

.service-link {
  color: #0b3d02;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.service-link:hover {
  color: #FFC107;
}

/* Farmer Section & Expert Section */
.farmer-section, .expert-section {
  padding: 60px 0;
}

.farmer-section {
  background-color: #f8f9fa;
}

.expert-section {
  background-color: #fff;
}

.feature-row {
  margin-bottom: 80px;
  transition: transform 0.4s ease;
}

.feature-row:hover {
  transform: translateY(-5px);
}

.feature-content h3 {
  font-size: 1.8rem;
  color: #0b3d02;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.feature-content h3::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 0;
  height: 3px;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.feature-row:hover .feature-content h3::after {
  width: 100%;
}

.feature-content p {
  color: #555;
  font-size: 1.1rem;
  line-height: 1.6;
}

.feature-image {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  min-height: 250px;
  object-fit: cover;
  background-color: #f0f0f0;
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.feature-row:hover .feature-image {
  transform: scale(1.03);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background-color: #0b3d02;
  color: white;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(255, 193, 7, 0.1));
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.cta-section:hover::before {
  transform: translateX(0);
}

.cta-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.cta-text {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto 30px;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.cta-buttons .btn {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-buttons .btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 991px) {
  .hero-title {
    font-size: 2.2rem;
  }
  
  .feature-row {
    margin-bottom: 60px;
  }
  
  .feature-content {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .services-hero {
    padding: 60px 0;
  }
  
  .hero-title {
    font-size: 1.8rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .cta-text {
    font-size: 1.1rem;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-buttons .btn {
    margin-bottom: 15px;
    width: 100%;
    max-width: 250px;
  }
}

/* Add these styles to handle image loading */
.hero-image, .feature-image {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  min-height: 300px;
  object-fit: cover;
  background-color: #f0f0f0;
}

.feature-image {
  min-height: 250px;
}

/* Additional keyframe animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Feature row animations */
.feature-row {
  transition: transform 0.4s ease;
  margin-bottom: 60px;
}

.feature-row:hover {
  transform: translateY(-5px);
}

.feature-content h3 {
  position: relative;
  display: inline-block;
}

.feature-content h3::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 0;
  height: 3px;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.feature-row:hover .feature-content h3::after {
  width: 100%;
}

/* AI Assistant Section Styles */
.ai-assistant-section {
  padding: 80px 0;
  background-color: #f8f9fa;
  position: relative;
}

.ai-assistant-section .section-subtitle {
  max-width: 700px;
  margin: 0 auto 40px;
  color: #6c757d;
}

.ai-assistant-image {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
}

.ai-badge {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: rgba(40, 167, 69, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.robot-icon {
  font-size: 1.2rem;
}

.ai-assistant-form-container {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.ai-assistant-form-container h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 700;
}

.ai-assistant-form .form-group {
  margin-bottom: 20px;
}

.ai-assistant-form label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: block;
}

.image-upload-container {
  position: relative;
}

.upload-icon {
  position: absolute;
  right: 15px;
  top: 10px;
  color: #28a745;
}

.image-preview {
  max-width: 200px;
  margin-top: 10px;
}

.image-preview img {
  width: 100%;
  border-radius: 5px;
}

.ai-response {
  background-color: #f0f9f0;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #28a745;
}

.ai-response h4 {
  color: #28a745;
  margin-bottom: 15px;
  font-weight: 600;
}

.response-content {
  line-height: 1.6;
  color: #495057;
  margin-bottom: 20px;
}

.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .ai-assistant-image {
    margin-bottom: 30px;
  }
}


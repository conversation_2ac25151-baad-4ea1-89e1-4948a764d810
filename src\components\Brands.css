.brands-container {
  background-color: #f8f9fa;
  padding: 60px 0;
}

.brand-divider {
  width: 80px;
  height: 3px;
  background-color: #28a745;
  margin: 15px auto 30px;
}

/* Slider container with hidden overflow */
.brands-slider-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: 20px 0;
}

/* The actual slider with animation */
.brands-slider {
  display: flex;
  animation: scrollBrands 30s linear infinite;
  width: fit-content;
}

/* Animation keyframes for continuous scrolling */
@keyframes scrollBrands {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%); /* Move exactly half the width to create seamless loop */
  }
}

/* Pause animation on hover (optional) */
.brands-slider-container:hover .brands-slider {
  animation-play-state: paused;
}

/* Brand item styling */
.brand-item {
  flex: 0 0 200px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  margin: 0 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.brand-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.brand-item img {
  max-width: 100%;
  max-height: 90px;
  object-fit: contain;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: filter 0.3s, opacity 0.3s;
}

.brand-item:hover img {
  filter: grayscale(0%);
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .brand-item {
    flex: 0 0 150px;
    height: 80px;
    margin: 0 10px;
  }
  
  .brand-item img {
    max-height: 80px;
  }
}


/* Container for the shop icons with pumping animation */
/* .shop-wrapper {
    position: relative;
    width: 200px;
    height: 200px;
    animation: pump 1.2s ease-in-out infinite;
  } */
  .shop-wrapper {
    position: relative;
    width: 200px;
    height: 200px;
    animation: pump 0.8s ease-in-out infinite; /* Faster heartbeat */
    z-index: 1;
  }
  
  /* Circles container inside .shop-wrapper */
  .shop-wrapper .pulse-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background-color: rgba(25, 135, 84, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(1);
    z-index: -1;
    animation: ripple 3s ease-out infinite;
  }
  
  /* Add delays for multiple circle waves */
  .shop-wrapper .pulse-circle:nth-child(1) {
    animation-delay: 0s;
  }
  .shop-wrapper .pulse-circle:nth-child(2) {
    animation-delay: 0.4s;
  }
  .shop-wrapper .pulse-circle:nth-child(3) {
    animation-delay: 0.8s;
  }
  
  /* Ripple animation */
  @keyframes ripple {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.5;
    }
    70% {
      transform: translate(-50%, -50%) scale(1.8);
      opacity: 0.1;
    }
    100% {
      opacity: 0;
    }
  }
  
  /* Pumping effect */
  @keyframes pump {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.07);
    }
  }
  
  
  
  /* Cart icon and text container */
  .cart-button {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    cursor: pointer;
  }
  
  .cart-icon {
    font-size: 2.5rem;
    color: #198754;
    transition: transform 0.3s;
  }
  
  .cart-icon:hover {
    transform: scale(1.1);
  }
  
  .cart-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: #198754;
    margin-top: 0.3rem;
  }
  
  /* Orbiting icons: seed, chemical, equipment (no pump here) */
  .orbit-icon {
    position: absolute;
    font-size: 1.3rem;
    transition: transform 0.2s;
    cursor: pointer;
  }
  
  .orbit-icon:hover {
    transform: scale(1.3);
  }
  
  /* Individual positions and colors */
  .seed {
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    color: #4caf50;
  }
  
  .chem {
    top: 25%;
    left: 20%;
    color: #ffc107;
  }
  
  .equip {
    top: 25%;
    right: 20%;
    color: #ff9800;
  }
  
  /* Heartbeat animation */
  @keyframes pump {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.07);
    }
  }
  